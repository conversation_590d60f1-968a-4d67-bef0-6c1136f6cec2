/* استيراد خط عربي عصري */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap');

:root {
  --primary: #4CAF50;
  --primary-dark: #45a049;
  --success: #28a745;
  --success-dark: #218838;
  --warning: #FFC107;
  --warning-dark: #e0a800;
  --gray-light: #f5f5f5;
  --gray: #ccc;
  --radius: 6px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: var(--gray-light);
  font-family: 'Cairo', sans-serif;
  color: #333;
}

.container {
  max-width: 500px;
  margin: 50px auto;
  padding: 0 15px;
}

.card {
  background: #fff;
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 25px;
}

h1 {
  font-size: 1.6rem;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
}

/* حقول النموذج */
.file-input-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.file-input-row input[type="text"] {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--gray);
  border-radius: var(--radius);
  font-size: 1rem;
}

.file-input-row input[type="file"] {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--gray);
  border-radius: var(--radius);
}

/* زر إضافة ملف */
.file-input-row button#addFileBtn {
  align-self: flex-start;
  padding: 6px 12px;
  border: 2px dashed var(--primary);
  background: transparent;
  color: var(--primary);
  border-radius: var(--radius);
  font-size: 1.2rem;
  cursor: pointer;
  transition: background 0.3s, border-color 0.3s, color 0.3s;
}
.file-input-row button#addFileBtn:hover {
  background: var(--primary);
  color: #fff;
  border-color: var(--primary-dark);
}

/* زر إزالة الملف */
.file-input-row button.removeBtn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #e74c3c;
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
.file-input-row button.removeBtn:hover {
  background: #c0392b;
}

/* أزرار عامة */
button.btn {
  width: 100%;
  padding: 12px;
  background: var(--primary);
  color: #fff;
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s;
}
button.btn:hover {
  background: var(--primary-dark);
}
button.btn-success {
  background: var(--success);
}
button.btn-success:hover {
  background: var(--success-dark);
}
button.btn-warning {
  background: var(--warning);
}
button.btn-warning:hover {
  background: var(--warning-dark);
}

.error {
  color: #c00;
  margin-top: 10px;
  text-align: center;
}

/* عرض الروابط */
.card.result {
  background: #e8f5e9;
  border: 1px solid #c8e6c9;
  padding: 20px;
  margin: 20px auto;
  max-width: 600px;
}
.card.result h2 {
  margin-bottom: 12px;
  text-align: center;
}
.card.result .note {
  margin: 8px 0 16px;
  font-style: italic;
  text-align: center;
}
.links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  padding: 12px 0;
  border-bottom: 1px dashed var(--gray);
}
.result-item:last-child {
  border-bottom: none;
}

.school-label {
  font-weight: 600;
  margin-bottom: 6px;
}

.link-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.result-input {
  flex: 1;
  min-width: 200px;
  padding: 8px;
  border: 1px solid var(--gray);
  border-radius: var(--radius);
}

.copy-btn,
.open-btn {
  padding: 8px 12px;
  border: none;
  border-radius: var(--radius);
  color: #fff;
  cursor: pointer;
  font-size: 0.9rem;
  white-space: nowrap;
  transition: background 0.3s;
  display: inline-block;   /* لإظهار العنصر كـ inline-block */
  margin: 0 auto;          /* لن يؤثر على inline-block بمفرده */
  background: var(--warning);
}

.copy-btn {
  background: var(--success);
}
.copy-btn:hover {
  background: var(--success-dark);
}

.open-btn {
  background: var(--warning);
  display: flex;
  align-items: center;
  gap: 4px;
}
.open-btn:hover {
  background: var(--warning-dark);
}
.no-underline {
  text-decoration: none;
}
p.back-link {
  text-align: center;
  margin-top: 16px;
}
p.back-link a {
  text-decoration: none;
  color: var(--primary);
}
/* في styles.css أو داخل <style> */
.search-open-wrapper {
  display: flex;
  justify-content: center; /* يوسّط المحتوى أفقياً */
  margin: 16px 0;
}

/* يزيل الخط السفلي ويجعل العنصر يظهر كزر */
.search-open-wrapper .no-underline {
  display: inline-block;
  text-decoration: none;
}

#logoutBtn {
  background: #d9534f;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}