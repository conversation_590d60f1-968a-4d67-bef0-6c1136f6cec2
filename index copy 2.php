<!-- <script>
    const token = localStorage.getItem("google_token");
    if (!token) {
      // لم يسجل الدخول
      window.location.href = "login.php";
    }
  </script> -->

  <h1>🎉 مرحبًا بك!</h1>
  <p>لقد تم تسجيل دخولك بحساب Google بنجاح.</p>
 

  <!-- <script>
    function logout() {
      localStorage.removeItem("google_token");
      window.location.href = "login.php";
    }
  </script> -->
<?php
// index.php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require __DIR__ . '/vendor/autoload.php';

// إعداد SQLite
try {
    $dbPath = __DIR__ . '/database.sqlite';
    if (!file_exists($dbPath)) {
        touch($dbPath);
        chmod($dbPath, 0666);
    }
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->exec("CREATE TABLE IF NOT EXISTS uploads (
        token TEXT PRIMARY KEY,
        school_name TEXT,
        file_path TEXT,
        created_at TEXT
    )");
} catch (PDOException $e) {
    exit('خطأ في فتح قاعدة البيانات: ' . $e->getMessage());
}

// توليد رمز قصير من 4 أحرف/أرقام
function generateUniqueToken(PDO $db): string {
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    do {
        $t = '';
        for ($i = 0; $i < 4; $i++) {
            $t .= $chars[random_int(0, strlen($chars) - 1)];
        }
        $stmt = $db->prepare("SELECT COUNT(*) FROM uploads WHERE token = :t");
        $stmt->execute([':t' => $t]);
        $exists = (int)$stmt->fetchColumn();
    } while ($exists);
    return $t;
}

// إذا تم إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $schoolNames = $_POST['school_names'] ?? [];
    $files       = $_FILES['pdf_files'] ?? null;
    if (empty($schoolNames) || !$files) {
        http_response_code(400);
        echo '<div class="container"><div class="card error">⚠️ الرجاء إدخال اسم المدرسة لكل ملف ورفع ملف واحد على الأقل.</div></div>';
        exit;
    }

    $results = [];
    $count = count($files['name']);
    for ($i = 0; $i < $count; $i++) {
        $school = trim($schoolNames[$i] ?? '');
        if ($school === '' || $files['error'][$i] !== UPLOAD_ERR_OK) continue;
        $ext = strtolower(pathinfo($files['name'][$i], PATHINFO_EXTENSION));
        if ($ext !== 'pdf') continue;

        $token = generateUniqueToken($db);
        $dest = __DIR__ . "/uploads/{$token}.pdf";
        move_uploaded_file($files['tmp_name'][$i], $dest);

        $stmt = $db->prepare("INSERT INTO uploads(token, school_name, file_path, created_at)
            VALUES(:t, :s, :p, datetime('now'))");
        $stmt->execute([':t'=>$token, ':s'=>$school, ':p'=>$dest]);

        $base = (isset($_SERVER['HTTPS']) ? 'https://' : 'http://')
              . $_SERVER['HTTP_HOST']
              . rtrim(dirname($_SERVER['REQUEST_URI']), '/\\')
              . '/search.php?t=';
        $results[] = ['school'=>$school, 'link'=>$base.$token];
    }

    // عرض النتائج
    $cnt = count($results);
    $plural = $cnt > 1 ? 'ات' : '';
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      
      <meta charset="UTF-8">
      <title>عرض روابط البحث</title>
      <link rel="stylesheet" href="assets/css/styles.css">
    </head>
    <body>
    
    <div class="container">
      <div class="card">
        <h2>✅ تم رفع <?= $cnt ?> ملف<?= $plural ?> بنجاح</h2>
        <p class="note">انسخ الرابط وأرسله لأولياء الأمور لعرض نتيجة أبنائهم بعد كتابة السجل المدني للطالب</p>
        <div class="links">
          <?php foreach ($results as $res): ?>
            <div class="result-item">
              <div class="school-label"><?= htmlspecialchars($res['school']) ?></div>
              <div class="link-row">
                <input type="text" class="result-input" value="<?= htmlspecialchars($res['link']) ?>" readonly>
                <button class="btn btn-success copy-btn" data-link="<?= htmlspecialchars($res['link']) ?>">نسخ</button>
              <!-- أضف هذا حول زر “اضغط هنا…” -->
<div class="search-open-wrapper">
  <a
    href="<?= htmlspecialchars($res['link'], ENT_QUOTES) ?>"
    target="_blank"
    class="open-btn no-underline"
  >
    اضغط هنا لفتح رابط صفحة البحث <span>🔗</span>
  </a>
</div>


              </div>
            </div>
          <?php endforeach; ?>
        </div>
        <p class="back-link">
        <a href="index.php">◀ رفع ملف آخر</a>
      </p>
      </div>
      
    </div>
    <script>
      document.querySelectorAll('.copy-btn').forEach(btn=> btn.addEventListener('click', ()=> {
        const link = btn.getAttribute('data-link');
        navigator.clipboard.writeText(link).then(()=> alert('تم نسخ الرابط'));
      }));
    </script>
    </body>
    </html>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>رفع ملفات شهادات المدرسة دفعة واحدة</title>
  <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
<div class="container">
  <div class="card">
    <h1>رفع ملفات شهادات المدرسة دفعة واحدة</h1>
    <form id="dynamicUploadForm" method="POST" enctype="multipart/form-data">
      <div class="file-input-row">
        <input type="text" name="school_names[]" placeholder="اسم المدرسة/الصف (حتى 4 كلمات)" required>
        <input type="file" name="pdf_files[]" accept="application/pdf" required>
        <button type="button" id="addFileBtn">+</button>
      </div>
      <button class="btn btn-primary" type="submit">رفع وإنشاء الروابط</button>
    </form>
  </div>
  <button id="logoutBtn" onclick="logout()">🚪 تسجيل الخروج</button>
</div>
<script>
  const form = document.getElementById('dynamicUploadForm');
  document.getElementById('addFileBtn').addEventListener('click', ()=>{
    const row = document.createElement('div');
    row.className = 'file-input-row';
    const txt = document.createElement('input'); txt.type='text'; txt.name='school_names[]'; txt.placeholder='اسم المدرسة/الصف'; txt.required=true;
    const file = document.createElement('input'); file.type='file'; file.name='pdf_files[]'; file.accept='application/pdf'; file.required=true;
    const rm = document.createElement('button'); rm.type='button'; rm.textContent='−'; rm.className='removeBtn'; rm.addEventListener('click', ()=>row.remove());
    row.append(txt, file, rm);
    form.insertBefore(row, form.querySelector('button[type="submit"]'));
  });
</script>



</body>
</html>
