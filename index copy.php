<?php
// index.php

// عرض الأخطاء لتسهيل التصحيح
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تضمين Composer autoload
require __DIR__ . '/vendor/autoload.php';

// إعداد SQLite
try {
    $dbPath = __DIR__ . '/database.sqlite';
    if (!file_exists($dbPath)) {
        touch($dbPath);
        chmod($dbPath, 0666);
    }
    $db = new PDO('sqlite:' . $dbPath);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->exec("CREATE TABLE IF NOT EXISTS uploads (
        token TEXT PRIMARY KEY,
        school_name TEXT,
        file_path TEXT,
        created_at TEXT
    )");
} catch (PDOException $e) {
    exit('خطأ في فتح قاعدة البيانات: ' . $e->getMessage());
}

// توليد رمز قصير
function generateUniqueToken(PDO $db): string {
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    do {
        $t = '';
        for ($i = 0; $i < 4; $i++) {
            $t .= $chars[random_int(0, strlen($chars) - 1)];
        }
        $stmt = $db->prepare("SELECT COUNT(*) FROM uploads WHERE token = :t");
        $stmt->execute([':t' => $t]);
        $exists = (int)$stmt->fetchColumn();
    } while ($exists);
    return $t;
}

// معالجة الرفع عبر AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $schoolName = trim($_POST['school_name'] ?? '');
    $file       = $_FILES['pdf_file'] ?? null;
    $error      = '';

    if ($schoolName === '') {
        $error = '⚠️ الرجاء إدخال اسم المدرسة.';
    } elseif (!$file || $file['error'] !== UPLOAD_ERR_OK) {
        $error = '⚠️ حدث خطأ أثناء رفع الملف.';
    } elseif (strtolower(pathinfo($file['name'], PATHINFO_EXTENSION)) !== 'pdf') {
        $error = '⚠️ الرجاء رفع ملف PDF فقط.';
    }

    if ($error) {
        http_response_code(400);
        echo '<p class="error">' . htmlspecialchars($error) . '</p>';
        exit;
    }

    // حفظ الملف وتسجيله في DB
    $token = generateUniqueToken($db);
    $dest  = __DIR__ . "/uploads/{$token}.pdf";
    move_uploaded_file($file['tmp_name'], $dest);
    $stmt = $db->prepare("INSERT INTO uploads(token, school_name, file_path, created_at)
        VALUES(:token, :school_name, :file_path, datetime('now'))");
    $stmt->execute([
        ':token'       => $token,
        ':school_name' => $schoolName,
        ':file_path'   => $dest
    ]);

    // بناء الرابط
    $base = (isset($_SERVER['HTTPS']) ? 'https://' : 'http://')
          . $_SERVER['HTTP_HOST']
          . rtrim(dirname($_SERVER['REQUEST_URI']), '/\\')
          . '/search.php?t=';
    $link = htmlspecialchars($base . $token);

    // استجابة HTML لعرض الرابط
    echo <<<HTML
<div class="container">
  <div class="card">
    <h1>✅ تم رفع الملف بنجاح</h1>
    <p>مدرسة: <strong>{$schoolName}</strong></p>
    <p>رابط البحث:</p>
    <div class="form-group">
      <input id="linkField" type="text" value="{$link}" readonly
             style="width:100%; padding:8px; border:1px solid var(--gray); border-radius:var(--radius);">
      <button id="copyBtn" type="button" class="btn" style="margin-top:8px;">نسخ الرابط</button>
     
    </div>
    <p style="text-align:center;"> <a href="{$link}" target="_blank" style="text-decoration: none; color: inherit;">إضغط هنا لفتح رابط صفحة البحث</a></p>
  </div>
</div>
HTML;
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>رفع شهادات المدرسة</title>
  <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
  <div class="container">
    <div class="card">
      <h1>رفع ملف شهادات المدرسة</h1>
      <form id="uploadForm" enctype="multipart/form-data">
        <div class="form-group">
          <label>اسم المدرسة:</label>
          <input type="text" name="school_name" required autofocus>
        </div>
        <div class="form-group">
          <label>اختيار ملف PDF:</label>
          <input type="file" name="pdf_file" accept="application/pdf" required>
        </div>
        <button class="btn" type="submit">رفع وإنشاء الرابط</button>

        <div id="progressWrapper" style="display:none; margin-top:15px;">
          <progress id="uploadProgress" value="0" max="100" style="width:100%;"></progress>
          <p id="progressText">0%</p>
        </div>

        <p class="error" id="errorMsg"></p>
      </form>
    </div>
  </div>

  <script>
  // دالة لتهيئة الأحداث بعد تحميل AJAX
  function initCopyOpenButtons() {
    const linkField = document.getElementById('linkField');
    const copyBtn   = document.getElementById('copyBtn');
    const openBtn   = document.getElementById('openBtn');
    if (copyBtn) {
      copyBtn.addEventListener('click', () => {
        linkField.select();
        document.execCommand('copy');
        alert('تم نسخ الرابط');
      });
    }
    if (openBtn) {
      openBtn.addEventListener('click', () => {
        window.open(linkField.value, '_blank');
      });
    }
  }

  document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const form = e.target;
    const data = new FormData(form);
    const xhr  = new XMLHttpRequest();
    document.getElementById('progressWrapper').style.display = 'block';
    xhr.upload.addEventListener('progress', function(evt) {
      if (evt.lengthComputable) {
        const percent = Math.round(evt.loaded / evt.total * 100);
        document.getElementById('uploadProgress').value   = percent;
        document.getElementById('progressText').textContent = percent + '%';
      }
    });
    xhr.addEventListener('load', function() {
      if (xhr.status === 200) {
        document.body.innerHTML = xhr.responseText;
        initCopyOpenButtons();
      } else {
        document.getElementById('errorMsg').textContent = 'حدث خطأ أثناء الرفع (' + xhr.status + ')';
      }
    });
    xhr.addEventListener('error', function() {
      document.getElementById('errorMsg').textContent = 'تعذّر الاتصال بالخادم.';
    });
    xhr.open('POST', 'index.php');
    xhr.send(data);
  });
  </script>
</body>
</html>
