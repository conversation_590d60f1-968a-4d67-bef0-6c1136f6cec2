<?php
// search.php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require __DIR__ . '/vendor/autoload.php';
use Smalot\PdfParser\Parser;
use setasign\Fpdi\Fpdi;

// 1) إعداد اتصال SQLite
try {
    $db = new PDO('sqlite:' . __DIR__ . '/database.sqlite');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    exit('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// 2) جلب رمز المدرسة من GET
$token = $_GET['t'] ?? '';
if (empty($token)) {
    exit('❌ رمز المدرسة مفقود.');
}

// 3) استعلام بيانات التحميل
$stmt = $db->prepare('SELECT school_name, file_path FROM uploads WHERE token = :t');
$stmt->execute([':t' => $token]);
$upload = $stmt->fetch(PDO::FETCH_ASSOC);

if (! $upload) {
    exit('❌ الرابط غير صالح أو منتهي.');
}

$schoolName = $upload['school_name'];
$error      = '';

// 4) عند إرسال البحث
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $civilId = trim($_POST['civil_id'] ?? '');
    if (! preg_match('/^\d{6,12}$/', $civilId)) {
        $error = '⚠️ أدخل رقم سجل مدني صالح (6–12 رقم).';
    } else {
        // استخراج النص من صفحات الـ PDF
        $parser = new Parser();

        // التحقق من وجود الملف أولاً
        if (!file_exists(__DIR__ . '/' . $upload['file_path'])) {
            $error = '⚠️ عذرًا، الملف غير موجود أو تم حذفه.';
        } else {
            try {
                $pdfObj = $parser->parseFile(__DIR__ . '/' . $upload['file_path']);
                $pages  = $pdfObj->getPages();

                // إعداد FPDI
                $pdfWriter = new Fpdi();
                $pdfWriter->setSourceFile(__DIR__ . '/' . $upload['file_path']);

                $found = false;
                $needle = preg_quote($civilId, '/');
                foreach ($pages as $index => $page) {
                    $text = mb_strtolower($page->getText() ?: '', 'UTF-8');
                    // تطابق صحيح 100% (لا يحيط به أرقام أخرى)
                    if (preg_match('/(?<!\d)' . $needle . '(?!\d)/u', $text)) {
                        $pdfWriter->AddPage();
                        $tplId = $pdfWriter->importPage($index + 1);
                        $pdfWriter->useTemplate($tplId);
                        $found = true;
                    }
                }

                if ($found) {
                    header('Content-Type: application/pdf');
                    header('Content-Disposition: inline; filename="result_' . $civilId . '.pdf"');
                    $pdfWriter->Output('I');
                    exit;
                } else {
                    $error = "⚠️ لم يُعثر على صفحة برقم السجل {$civilId}.";
                }
            } catch (Exception $e) {
                $error = '⚠️ حدث خطأ أثناء معالجة الملف: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>بحث طالب – <?= htmlspecialchars($schoolName, ENT_QUOTES) ?></title>
  <link rel="stylesheet" href="assets/css/styles.css">
  <style>
    .container { max-width: 420px; margin: 50px auto; }
    .card { padding: 20px; }
    h1 { text-align: center; margin-bottom: 20px; }

    /* تنسيق النموذج في المنتصف */
    .form-group {
      text-align: center;
      margin-bottom: 12px;
    }
    .form-group label {
      font-weight: 500;
      display: block;
      margin-bottom: 6px;
    }

    /* حقل البحث أصغر وفي المنتصف */
    input#civil_id {
      width: 60%;
      display: block;
      margin: 0 auto 10px;
      padding: 10px;
      border: 1px solid var(--gray);
      border-radius: var(--radius);
      font-size: 1rem;
    }

    /* زر البحث أصغر ومركزي */
    button.btn-primary {
      width: auto;
      min-width: 40%;
      display: block;
      margin: 0 auto;
      padding: 12px 20px;
      background: var(--primary);
      color: #fff;
      border: none;
      border-radius: var(--radius);
      font-size: 1rem;
      cursor: pointer;
      transition: background .3s;
    }
    button.btn-primary:hover {
      background: var(--primary-dark);
    }

    .error {
      color: #c00;
      margin-top: 10px;
      text-align: center;
    }
    p.back-link {
      text-align: center;
      margin-top: 16px;
    }
    p.back-link a {
      text-decoration: none;
      color: var(--primary);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <h1><?= htmlspecialchars($schoolName, ENT_QUOTES) ?></h1>
      <form method="POST">
        <div class="form-group">
          <label for="civil_id">رقم السجل المدني:</label>
          <input
            type="text"
            id="civil_id"
            name="civil_id"
            pattern="\d{6,12}"
            required
            autofocus
          >
        </div>
        <button class="btn-primary" type="submit">ابحث</button>
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($error)): ?>
          <p class="error"><?= htmlspecialchars($error, ENT_QUOTES) ?></p>
        <?php endif; ?>
      </form>
      <!-- <p class="back-link">
        <a href="index.php">◀ رفع ملف آخر</a>
      </p> -->
    </div>
  </div>
</body>
</html>
