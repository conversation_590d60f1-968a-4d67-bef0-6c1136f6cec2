<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'FPDF' => $vendorDir . '/setasign/fpdf/fpdf.php',
    'Smalot\\PdfParser\\Config' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Config.php',
    'Smalot\\PdfParser\\Document' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Document.php',
    'Smalot\\PdfParser\\Element' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element.php',
    'Smalot\\PdfParser\\Element\\ElementArray' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementArray.php',
    'Smalot\\PdfParser\\Element\\ElementBoolean' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementBoolean.php',
    'Smalot\\PdfParser\\Element\\ElementDate' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementDate.php',
    'Smalot\\PdfParser\\Element\\ElementHexa' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementHexa.php',
    'Smalot\\PdfParser\\Element\\ElementMissing' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementMissing.php',
    'Smalot\\PdfParser\\Element\\ElementName' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementName.php',
    'Smalot\\PdfParser\\Element\\ElementNull' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementNull.php',
    'Smalot\\PdfParser\\Element\\ElementNumeric' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementNumeric.php',
    'Smalot\\PdfParser\\Element\\ElementString' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementString.php',
    'Smalot\\PdfParser\\Element\\ElementStruct' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementStruct.php',
    'Smalot\\PdfParser\\Element\\ElementXRef' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementXRef.php',
    'Smalot\\PdfParser\\Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding.php',
    'Smalot\\PdfParser\\Encoding\\AbstractEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/AbstractEncoding.php',
    'Smalot\\PdfParser\\Encoding\\EncodingLocator' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/EncodingLocator.php',
    'Smalot\\PdfParser\\Encoding\\ISOLatin1Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/ISOLatin1Encoding.php',
    'Smalot\\PdfParser\\Encoding\\ISOLatin9Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/ISOLatin9Encoding.php',
    'Smalot\\PdfParser\\Encoding\\MacRomanEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/MacRomanEncoding.php',
    'Smalot\\PdfParser\\Encoding\\PDFDocEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/PDFDocEncoding.php',
    'Smalot\\PdfParser\\Encoding\\PostScriptGlyphs' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/PostScriptGlyphs.php',
    'Smalot\\PdfParser\\Encoding\\StandardEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/StandardEncoding.php',
    'Smalot\\PdfParser\\Encoding\\WinAnsiEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/WinAnsiEncoding.php',
    'Smalot\\PdfParser\\Exception\\EmptyPdfException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/EmptyPdfException.php',
    'Smalot\\PdfParser\\Exception\\EncodingNotFoundException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/EncodingNotFoundException.php',
    'Smalot\\PdfParser\\Exception\\InvalidDictionaryObjectException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/InvalidDictionaryObjectException.php',
    'Smalot\\PdfParser\\Exception\\MissingCatalogException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/MissingCatalogException.php',
    'Smalot\\PdfParser\\Exception\\MissingPdfHeaderException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/MissingPdfHeaderException.php',
    'Smalot\\PdfParser\\Exception\\NotImplementedException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/NotImplementedException.php',
    'Smalot\\PdfParser\\Font' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font.php',
    'Smalot\\PdfParser\\Font\\FontCIDFontType0' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontCIDFontType0.php',
    'Smalot\\PdfParser\\Font\\FontCIDFontType2' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontCIDFontType2.php',
    'Smalot\\PdfParser\\Font\\FontTrueType' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontTrueType.php',
    'Smalot\\PdfParser\\Font\\FontType0' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType0.php',
    'Smalot\\PdfParser\\Font\\FontType1' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType1.php',
    'Smalot\\PdfParser\\Font\\FontType3' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType3.php',
    'Smalot\\PdfParser\\Header' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Header.php',
    'Smalot\\PdfParser\\PDFObject' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/PDFObject.php',
    'Smalot\\PdfParser\\Page' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Page.php',
    'Smalot\\PdfParser\\Pages' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Pages.php',
    'Smalot\\PdfParser\\Parser' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Parser.php',
    'Smalot\\PdfParser\\RawData\\FilterHelper' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/RawData/FilterHelper.php',
    'Smalot\\PdfParser\\RawData\\RawDataParser' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/RawData/RawDataParser.php',
    'Smalot\\PdfParser\\XObject\\Form' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/XObject/Form.php',
    'Smalot\\PdfParser\\XObject\\Image' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/XObject/Image.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'setasign\\Fpdi\\FpdfTpl' => $vendorDir . '/setasign/fpdi/src/FpdfTpl.php',
    'setasign\\Fpdi\\FpdfTplTrait' => $vendorDir . '/setasign/fpdi/src/FpdfTplTrait.php',
    'setasign\\Fpdi\\FpdfTrait' => $vendorDir . '/setasign/fpdi/src/FpdfTrait.php',
    'setasign\\Fpdi\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Fpdi.php',
    'setasign\\Fpdi\\FpdiException' => $vendorDir . '/setasign/fpdi/src/FpdiException.php',
    'setasign\\Fpdi\\FpdiTrait' => $vendorDir . '/setasign/fpdi/src/FpdiTrait.php',
    'setasign\\Fpdi\\GraphicsState' => $vendorDir . '/setasign/fpdi/src/GraphicsState.php',
    'setasign\\Fpdi\\Math\\Matrix' => $vendorDir . '/setasign/fpdi/src/Math/Matrix.php',
    'setasign\\Fpdi\\Math\\Vector' => $vendorDir . '/setasign/fpdi/src/Math/Vector.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\AbstractReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/AbstractReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\CrossReference' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/CrossReference.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\CrossReferenceException' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/CrossReferenceException.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\FixedReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/FixedReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\LineReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/LineReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\ReaderInterface' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/ReaderInterface.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Ascii85' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Ascii85.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Ascii85Exception' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Ascii85Exception.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\AsciiHex' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/AsciiHex.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FilterException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FilterException.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FilterInterface' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FilterInterface.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Flate' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Flate.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FlateException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FlateException.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Lzw' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Lzw.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\LzwException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/LzwException.php',
    'setasign\\Fpdi\\PdfParser\\PdfParser' => $vendorDir . '/setasign/fpdi/src/PdfParser/PdfParser.php',
    'setasign\\Fpdi\\PdfParser\\PdfParserException' => $vendorDir . '/setasign/fpdi/src/PdfParser/PdfParserException.php',
    'setasign\\Fpdi\\PdfParser\\StreamReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/StreamReader.php',
    'setasign\\Fpdi\\PdfParser\\Tokenizer' => $vendorDir . '/setasign/fpdi/src/PdfParser/Tokenizer.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfArray' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfArray.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfBoolean' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfBoolean.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfDictionary' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfDictionary.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfHexString' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfHexString.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfIndirectObject' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfIndirectObject.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfIndirectObjectReference' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfIndirectObjectReference.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfName' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfName.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfNull' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfNull.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfNumeric' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfNumeric.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfStream' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfStream.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfString' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfString.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfToken' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfToken.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfType' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfType.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfTypeException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfTypeException.php',
    'setasign\\Fpdi\\PdfReader\\DataStructure\\Rectangle' => $vendorDir . '/setasign/fpdi/src/PdfReader/DataStructure/Rectangle.php',
    'setasign\\Fpdi\\PdfReader\\Page' => $vendorDir . '/setasign/fpdi/src/PdfReader/Page.php',
    'setasign\\Fpdi\\PdfReader\\PageBoundaries' => $vendorDir . '/setasign/fpdi/src/PdfReader/PageBoundaries.php',
    'setasign\\Fpdi\\PdfReader\\PdfReader' => $vendorDir . '/setasign/fpdi/src/PdfReader/PdfReader.php',
    'setasign\\Fpdi\\PdfReader\\PdfReaderException' => $vendorDir . '/setasign/fpdi/src/PdfReader/PdfReaderException.php',
    'setasign\\Fpdi\\TcpdfFpdi' => $vendorDir . '/setasign/fpdi/src/TcpdfFpdi.php',
    'setasign\\Fpdi\\Tcpdf\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Tcpdf/Fpdi.php',
    'setasign\\Fpdi\\Tfpdf\\FpdfTpl' => $vendorDir . '/setasign/fpdi/src/Tfpdf/FpdfTpl.php',
    'setasign\\Fpdi\\Tfpdf\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Tfpdf/Fpdi.php',
);
