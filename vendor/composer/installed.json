{"packages": [{"name": "setasign/fpdf", "version": "1.8.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDF.git", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDF/zipball/0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "reference": "0838e0ee4925716fcbbc50ad9e1799b5edfae0a0", "shasum": ""}, "require": {"ext-gd": "*", "ext-zlib": "*"}, "time": "2023-06-26T14:44:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["fpdf.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://fpdf.org/"}], "description": "FPDF is a PHP class which allows to generate PDF files with pure PHP. F from FPDF stands for Free: you may use it for any kind of usage and modify it to suit your needs.", "homepage": "http://www.fpdf.org", "keywords": ["fpdf", "pdf"], "support": {"source": "https://github.com/Setasign/FPDF/tree/1.8.6"}, "install-path": "../setasign/fpdf"}, {"name": "setasign/fpdi", "version": "v2.6.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/67c31f5e50c93c20579ca9e23035d8c540b51941", "reference": "67c31f5e50c93c20579ca9e23035d8c540b51941", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2025-02-05T13:22:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "install-path": "../setasign/fpdi"}, {"name": "setasign/fpdi-fpdf", "version": "v2.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI-FPDF.git", "reference": "f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI-FPDF/zipball/f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357", "reference": "f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357", "shasum": ""}, "require": {"setasign/fpdf": "^1.8.2", "setasign/fpdi": "^2.3"}, "time": "2020-02-19T12:21:53+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "Kind of metadata package for dependencies of the latest versions of FPDI and FPDF.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"source": "https://github.com/Setasign/FPDI-FPDF/tree/v2.3.0"}, "abandoned": true, "install-path": "../setasign/fpdi-fpdf"}, {"name": "smalot/pdfparser", "version": "v2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/8440edbf58c8596074e78ada38dcb0bd041a5948", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "time": "2025-03-31T13:16:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.12.0"}, "install-path": "../smalot/pdfparser"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}], "dev": false, "dev-package-names": []}