<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdf' => array(
            'pretty_version' => '1.8.6',
            'version' => '1.8.6.0',
            'reference' => '0838e0ee4925716fcbbc50ad9e1799b5edfae0a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '67c31f5e50c93c20579ca9e23035d8c540b51941',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdi-fpdf' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => 'f2fdc44e4d5247a3bb55ed2c2c1396ef05c02357',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi-fpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'smalot/pdfparser' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => '8440edbf58c8596074e78ada38dcb0bd041a5948',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smalot/pdfparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
