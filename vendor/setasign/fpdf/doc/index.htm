<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Documentation</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Documentation</h1>
<a href="__construct.htm">__construct</a> - constructor<br>
<a href="acceptpagebreak.htm">AcceptPageBreak</a> - accept or not automatic page break<br>
<a href="addfont.htm">AddFont</a> - add a new font<br>
<a href="addlink.htm">AddLink</a> - create an internal link<br>
<a href="addpage.htm">AddPage</a> - add a new page<br>
<a href="aliasnbpages.htm">AliasNbPages</a> - define an alias for number of pages<br>
<a href="cell.htm">Cell</a> - print a cell<br>
<a href="close.htm">Close</a> - terminate the document<br>
<a href="error.htm">Error</a> - fatal error<br>
<a href="footer.htm">Footer</a> - page footer<br>
<a href="getpageheight.htm">GetPageHeight</a> - get current page height<br>
<a href="getpagewidth.htm">GetPageWidth</a> - get current page width<br>
<a href="getstringwidth.htm">GetStringWidth</a> - compute string length<br>
<a href="getx.htm">GetX</a> - get current x position<br>
<a href="gety.htm">GetY</a> - get current y position<br>
<a href="header.htm">Header</a> - page header<br>
<a href="image.htm">Image</a> - output an image<br>
<a href="line.htm">Line</a> - draw a line<br>
<a href="link.htm">Link</a> - put a link<br>
<a href="ln.htm">Ln</a> - line break<br>
<a href="multicell.htm">MultiCell</a> - print text with line breaks<br>
<a href="output.htm">Output</a> - save or send the document<br>
<a href="pageno.htm">PageNo</a> - page number<br>
<a href="rect.htm">Rect</a> - draw a rectangle<br>
<a href="setauthor.htm">SetAuthor</a> - set the document author<br>
<a href="setautopagebreak.htm">SetAutoPageBreak</a> - set the automatic page breaking mode<br>
<a href="setcompression.htm">SetCompression</a> - turn compression on or off<br>
<a href="setcreator.htm">SetCreator</a> - set document creator<br>
<a href="setdisplaymode.htm">SetDisplayMode</a> - set display mode<br>
<a href="setdrawcolor.htm">SetDrawColor</a> - set drawing color<br>
<a href="setfillcolor.htm">SetFillColor</a> - set filling color<br>
<a href="setfont.htm">SetFont</a> - set font<br>
<a href="setfontsize.htm">SetFontSize</a> - set font size<br>
<a href="setkeywords.htm">SetKeywords</a> - associate keywords with document<br>
<a href="setleftmargin.htm">SetLeftMargin</a> - set left margin<br>
<a href="setlinewidth.htm">SetLineWidth</a> - set line width<br>
<a href="setlink.htm">SetLink</a> - set internal link destination<br>
<a href="setmargins.htm">SetMargins</a> - set margins<br>
<a href="setrightmargin.htm">SetRightMargin</a> - set right margin<br>
<a href="setsubject.htm">SetSubject</a> - set document subject<br>
<a href="settextcolor.htm">SetTextColor</a> - set text color<br>
<a href="settitle.htm">SetTitle</a> - set document title<br>
<a href="settopmargin.htm">SetTopMargin</a> - set top margin<br>
<a href="setx.htm">SetX</a> - set current x position<br>
<a href="setxy.htm">SetXY</a> - set current x and y positions<br>
<a href="sety.htm">SetY</a> - set current y position and optionally reset x<br>
<a href="text.htm">Text</a> - print a string<br>
<a href="write.htm">Write</a> - print flowing text<br>
</body>
</html>
