<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Text</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Text</h1>
<code>Text(<b>float</b> x, <b>float</b> y, <b>string</b> txt)</code>
<h2>Description</h2>
Prints a character string. The origin is on the left of the first character, on the baseline.
This method allows to place a string precisely on the page, but it is usually easier to use
Cell(), MultiCell() or Write() which are the standard methods to print text.
<h2>Parameters</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
Abscissa of the origin.
</dd>
<dt><code>y</code></dt>
<dd>
Ordinate of the origin.
</dd>
<dt><code>txt</code></dt>
<dd>
String to print.
</dd>
</dl>
<h2>See also</h2>
<a href="setfont.htm">SetFont</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="write.htm">Write</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
