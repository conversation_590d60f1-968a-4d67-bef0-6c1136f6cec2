name: "C<PERSON>"

on: [push, pull_request]

env:
  fail-fast: true

jobs:
  phpunit:
    name: "P<PERSON>Unit (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"
          - "8.3"
          - "8.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "none"
          tools: composer:v2
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v2"

      - name: "Install dev tools"
        run: "make install-dev-tools"

      - name: "Run PHPUnit"
        run: "make run-phpunit"

  phpunit-lower-php:
    name: "PHPUnit (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.1"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "none"
          tools: composer:v2
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v2"

      - name: "Install dev tools"
        run: "make install-dev-tools"

      - name: "Run PHPUnit"
        run: "make run-phpunit"

  phpunit-coverage:
    name: "PHPUnit coverage (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP with PCOV"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "xdebug"
          tools: composer:v2
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v2"

      - name: "Install dev tools"
        run: "make install-dev-tools"

      - name: "Run PHPUnit"
        run: make run-phpunit ARGS="-v --coverage-clover coverage/clover.xml"

  phpunit-composerv2:
    name: "PHPUnit Composer v2 (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "none"
          tools: composer:v2
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v2"

      - name: "Install dev tools"
        run: "make install-dev-tools"

      - name: "Run PHPUnit"
        run: "make run-phpunit"

  alt-autoload:
    name: "Tests alternative autoloader (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"
          - "8.3"
          - "8.4"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "none"

      - name: "Test alt-autoload"
        run: "php tests/AltAutoloading/AltAutoloadCheck.php"

  phpunit-lowest:
    name: "PHPUnit lowest deps (PHP ${{ matrix.php }})"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php:
          - "7.3"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v3"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          coverage: "none"
          tools: composer:v2
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v2"
        with:
          dependency-versions: "lowest"

      - name: "Install dev tools"
        run: "make install-dev-tools"

      - name: "Run PHPUnit"
        run: "make run-phpunit"

  windows-tests:
    name: Windows-Tests with PHP ${{ matrix.php-versions }}
    runs-on: windows-2019

    strategy:
      fail-fast: true
      matrix:
        php-versions: ['7.1', '7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          ini-values: memory_limit=1G

      - name: Install Composer dependencies (root)
        run: composer update --no-progress --no-suggest --prefer-dist --optimize-autoloader

      - name: Install Composer dependencies (dev-tools)
        run: composer update --working-dir=dev-tools

      - name: Tests
        run: dev-tools/vendor/bin/phpunit -c phpunit-windows.xml --exclude-group linux-only
